import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  User, Mail, Lock, Phone, Calendar, MapPin,
  Building, Clock, Eye, EyeOff, ChevronLeft,
  ChevronRight, AlertCircle, Check, Sparkles, Zap,
  Crown, Star, Gem, Heart, Flame, Shield, Gift,
  Package, Truck, TrendingUp, Bot, UserPlus,
  Rocket, Lightning, Award, Target
} from 'lucide-react';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import { apiService, type SignupRequest } from '../../services/api';

interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
  username: string;
  dateOfBirth: string;
  gender: string;
  address: string;
  city: string;
  country: string;
  role: 'customer' | 'supplier';
  storeName?: string;
  businessType?: string;
  openHours?: string;
  location?: [number, number]; // [longitude, latitude]
}

const SignupPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [locationLoading, setLocationLoading] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [animationPhase, setAnimationPhase] = useState<'initial' | 'welcome' | 'form'>('initial');

  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    trigger,
  } = useForm<SignupFormData>({
    defaultValues: {
      role: 'customer',
      country: 'Jordan',
    },
  });

  const watchedRole = watch('role');
  const watchedPassword = watch('password');

  // Enhanced animation sequence
  useEffect(() => {
    const timer1 = setTimeout(() => setAnimationPhase('welcome'), 1000);
    const timer2 = setTimeout(() => setAnimationPhase('form'), 2000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  // Location functions
  const requestLocation = async () => {
    setLocationLoading(true);
    setError('');

    try {
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported by this browser');
      }

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        });
      });

      const location: [number, number] = [
        position.coords.longitude,
        position.coords.latitude
      ];

      setUserLocation(location);
    } catch (error) {
      console.error('Location error:', error);
      setError('Unable to get your location. You can skip this step or try again.');
    } finally {
      setLocationLoading(false);
    }
  };

  const skipLocation = () => {
    setUserLocation(null);
  };

  const totalSteps = watchedRole === 'supplier' ? 5 : 4;

  const nextStep = async () => {
    const fieldsToValidate = getFieldsForStep(currentStep);
    const isValid = await trigger(fieldsToValidate);

    if (isValid) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
      setError('');
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
    setError('');
  };

  const getFieldsForStep = (step: number): (keyof SignupFormData)[] => {
    switch (step) {
      case 1:
        return ['firstName', 'lastName', 'email', 'phoneNumber'];
      case 2:
        return ['password', 'confirmPassword', 'username'];
      case 3:
        return ['dateOfBirth', 'gender', 'address', 'city', 'country', 'role'];
      case 4:
        return watchedRole === 'supplier' ? ['storeName', 'businessType', 'openHours'] : [];
      case 5:
        return []; // Location step - no required fields
      default:
        return [];
    }
  };

  const onSubmit = async (data: SignupFormData) => {
    setIsLoading(true);
    setError('');

    try {
      const signupData: SignupRequest = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phoneNumber: data.phoneNumber,
        password: data.password,
        username: data.username,
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
        address: data.address,
        city: data.city,
        country: data.country,
        role: data.role,
        storeName: data.storeName,
        businessType: data.businessType,
        openHours: data.openHours,
        location: userLocation || undefined,
        notifications: true,
      };

      const response = await apiService.signup(signupData);

      if (response.success) {
        navigate('/auth/email-verification', {
          state: { email: data.email }
        });
      } else {
        setError(response.message || 'Signup failed. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="space-y-6"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-center mb-6"
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-block mb-3"
              >
                <User size={48} className="text-primary-500" />
              </motion.div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Personal Information</h3>
              <p className="text-gray-600">Let's start with the basics about you</p>
            </motion.div>

            <div className="grid grid-cols-2 gap-4">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className="relative group"
              >
                <motion.div
                  className={`absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-2xl transition-all duration-300 ${
                    focusedField === 'firstName' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                  }`}
                />
                <div className="relative">
                  <Input
                    label="First Name"
                    placeholder="John"
                    icon={<User size={18} className="text-primary-500" />}
                    error={errors.firstName?.message}
                    onFocus={() => setFocusedField('firstName')}
                    className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl transition-all duration-300"
                    {...register('firstName', {
                      required: 'First name is required',
                      onBlur: () => setFocusedField(null)
                    })}
                  />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="relative group"
              >
                <motion.div
                  className={`absolute inset-0 bg-gradient-to-r from-secondary-500/10 to-third-500/10 rounded-2xl transition-all duration-300 ${
                    focusedField === 'lastName' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                  }`}
                />
                <div className="relative">
                  <Input
                    label="Last Name"
                    placeholder="Doe"
                    icon={<User size={18} className="text-secondary-500" />}
                    error={errors.lastName?.message}
                    onFocus={() => setFocusedField('lastName')}
                    className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-secondary-300 focus:border-secondary-500 rounded-2xl transition-all duration-300"
                    {...register('lastName', {
                      required: 'Last name is required',
                      onBlur: () => setFocusedField(null)
                    })}
                  />
                </div>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-third-500/10 to-primary-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'email' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <Input
                  label="Email"
                  type="email"
                  placeholder="<EMAIL>"
                  icon={<Mail size={18} className="text-third-500" />}
                  error={errors.email?.message}
                  onFocus={() => setFocusedField('email')}
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-third-300 focus:border-third-500 rounded-2xl transition-all duration-300"
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address',
                    },
                    onBlur: () => setFocusedField(null)
                  })}
                />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'phoneNumber' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <Input
                  label="Phone Number"
                  type="tel"
                  placeholder="+962 7X XXX XXXX"
                  icon={<Phone size={18} className="text-primary-500" />}
                  error={errors.phoneNumber?.message}
                  onFocus={() => setFocusedField('phoneNumber')}
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl transition-all duration-300"
                  {...register('phoneNumber', {
                    required: 'Phone number is required',
                    onBlur: () => setFocusedField(null)
                  })}
                />
              </div>
            </motion.div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="space-y-6"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-center mb-6"
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 10, -10, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-block mb-3"
              >
                <Shield size={48} className="text-secondary-500" />
              </motion.div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Security Setup</h3>
              <p className="text-gray-600">Create your secure login credentials</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-secondary-500/10 to-third-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'username' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <Input
                  label="Username"
                  placeholder="johndoe"
                  icon={<User size={18} className="text-secondary-500" />}
                  error={errors.username?.message}
                  onFocus={() => setFocusedField('username')}
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-secondary-300 focus:border-secondary-500 rounded-2xl transition-all duration-300"
                  {...register('username', {
                    required: 'Username is required',
                    onBlur: () => setFocusedField(null)
                  })}
                />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-third-500/10 to-primary-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'password' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <Input
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter password"
                  icon={<Lock size={18} className="text-third-500" />}
                  error={errors.password?.message}
                  onFocus={() => setFocusedField('password')}
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-third-300 focus:border-third-500 rounded-2xl transition-all duration-300"
                  {...register('password', {
                    required: 'Password is required',
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters',
                    },
                    onBlur: () => setFocusedField(null)
                  })}
                />
                <motion.button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-9 text-gray-400 hover:text-gray-600 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </motion.button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'confirmPassword' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <Input
                  label="Confirm Password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="Confirm password"
                  icon={<Lock size={18} className="text-primary-500" />}
                  error={errors.confirmPassword?.message}
                  onFocus={() => setFocusedField('confirmPassword')}
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl transition-all duration-300"
                  {...register('confirmPassword', {
                    required: 'Please confirm your password',
                    validate: value =>
                      value === watchedPassword || 'Passwords do not match',
                    onBlur: () => setFocusedField(null)
                  })}
                />
                <motion.button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-4 top-9 text-gray-400 hover:text-gray-600 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </motion.button>
              </div>
            </motion.div>

            {/* Password Strength Indicator */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-4 border border-gray-200"
            >
              <div className="flex items-center gap-2 mb-2">
                <Shield size={16} className="text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Password Security</span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                <div className="flex items-center gap-1">
                  <div className={`w-2 h-2 rounded-full ${watchedPassword && watchedPassword.length >= 6 ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span>6+ characters</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className={`w-2 h-2 rounded-full ${watchedPassword && /[A-Z]/.test(watchedPassword) ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span>Uppercase letter</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className={`w-2 h-2 rounded-full ${watchedPassword && /[0-9]/.test(watchedPassword) ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span>Number</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className={`w-2 h-2 rounded-full ${watchedPassword && /[!@#$%^&*]/.test(watchedPassword) ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span>Special character</span>
                </div>
              </div>
            </motion.div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="space-y-6"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-center mb-6"
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, -5, 5, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-block mb-3"
              >
                <Target size={48} className="text-third-500" />
              </motion.div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Personal Details</h3>
              <p className="text-gray-600">Tell us more about yourself</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-third-500/10 to-primary-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'dateOfBirth' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <Input
                  label="Date of Birth"
                  type="date"
                  icon={<Calendar size={18} className="text-third-500" />}
                  error={errors.dateOfBirth?.message}
                  onFocus={() => setFocusedField('dateOfBirth')}
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-third-300 focus:border-third-500 rounded-2xl transition-all duration-300"
                  {...register('dateOfBirth', {
                    required: 'Date of birth is required',
                    onBlur: () => setFocusedField(null)
                  })}
                />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'gender' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gender
                </label>
                <select
                  className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500/20"
                  onFocus={() => setFocusedField('gender')}
                  {...register('gender', {
                    required: 'Gender is required',
                    onBlur: () => setFocusedField(null)
                  })}
                >
                  <option value="">Select gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
                {errors.gender && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-1 text-sm text-red-600"
                  >
                    {errors.gender.message}
                  </motion.p>
                )}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-secondary-500/10 to-third-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'address' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <Input
                  label="Address"
                  placeholder="Street address"
                  icon={<MapPin size={18} className="text-secondary-500" />}
                  error={errors.address?.message}
                  onFocus={() => setFocusedField('address')}
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-secondary-300 focus:border-secondary-500 rounded-2xl transition-all duration-300"
                  {...register('address', {
                    required: 'Address is required',
                    onBlur: () => setFocusedField(null)
                  })}
                />
              </div>
            </motion.div>

            <div className="grid grid-cols-2 gap-4">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
                className="relative group"
              >
                <motion.div
                  className={`absolute inset-0 bg-gradient-to-r from-third-500/10 to-primary-500/10 rounded-2xl transition-all duration-300 ${
                    focusedField === 'city' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                  }`}
                />
                <div className="relative">
                  <Input
                    label="City"
                    placeholder="Amman"
                    icon={<MapPin size={18} className="text-third-500" />}
                    error={errors.city?.message}
                    onFocus={() => setFocusedField('city')}
                    className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-third-300 focus:border-third-500 rounded-2xl transition-all duration-300"
                    {...register('city', {
                      required: 'City is required',
                      onBlur: () => setFocusedField(null)
                    })}
                  />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
                className="relative group"
              >
                <motion.div
                  className={`absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-2xl transition-all duration-300 ${
                    focusedField === 'country' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                  }`}
                />
                <div className="relative">
                  <Input
                    label="Country"
                    placeholder="Jordan"
                    icon={<MapPin size={18} className="text-primary-500" />}
                    error={errors.country?.message}
                    onFocus={() => setFocusedField('country')}
                    className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl transition-all duration-300"
                    {...register('country', {
                      required: 'Country is required',
                      onBlur: () => setFocusedField(null)
                    })}
                  />
                </div>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="space-y-4"
            >
              <div className="text-center">
                <h4 className="text-lg font-bold text-gray-800 mb-2">Choose Your Role</h4>
                <p className="text-gray-600 text-sm">Select how you want to use BolTalab</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <motion.label
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`relative flex flex-col items-center p-6 border-2 rounded-2xl cursor-pointer transition-all duration-300 ${
                    watchedRole === 'customer'
                      ? 'border-primary-500 bg-gradient-to-br from-primary-50 to-secondary-50 shadow-lg'
                      : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                  }`}
                >
                  <input
                    type="radio"
                    value="customer"
                    {...register('role', { required: 'Please select account type' })}
                    className="sr-only"
                  />
                  <motion.div
                    animate={watchedRole === 'customer' ? {
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    } : {}}
                    transition={{ duration: 2, repeat: watchedRole === 'customer' ? Infinity : 0 }}
                    className="mb-3"
                  >
                    <Package size={32} className={`${
                      watchedRole === 'customer' ? 'text-primary-500' : 'text-gray-400'
                    }`} />
                  </motion.div>
                  <div className="text-center">
                    <div className={`font-bold text-lg mb-1 ${
                      watchedRole === 'customer' ? 'text-primary-700' : 'text-gray-700'
                    }`}>
                      Customer
                    </div>
                    <div className={`text-sm ${
                      watchedRole === 'customer' ? 'text-primary-600' : 'text-gray-500'
                    }`}>
                      Order and receive deliveries
                    </div>
                  </div>
                  {watchedRole === 'customer' && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute top-2 right-2"
                    >
                      <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                        <Check size={14} className="text-white" />
                      </div>
                    </motion.div>
                  )}
                </motion.label>

                <motion.label
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`relative flex flex-col items-center p-6 border-2 rounded-2xl cursor-pointer transition-all duration-300 ${
                    watchedRole === 'supplier'
                      ? 'border-secondary-500 bg-gradient-to-br from-secondary-50 to-third-50 shadow-lg'
                      : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                  }`}
                >
                  <input
                    type="radio"
                    value="supplier"
                    {...register('role', { required: 'Please select account type' })}
                    className="sr-only"
                  />
                  <motion.div
                    animate={watchedRole === 'supplier' ? {
                      scale: [1, 1.1, 1],
                      rotate: [0, -5, 5, 0]
                    } : {}}
                    transition={{ duration: 2, repeat: watchedRole === 'supplier' ? Infinity : 0 }}
                    className="mb-3"
                  >
                    <Building size={32} className={`${
                      watchedRole === 'supplier' ? 'text-secondary-500' : 'text-gray-400'
                    }`} />
                  </motion.div>
                  <div className="text-center">
                    <div className={`font-bold text-lg mb-1 ${
                      watchedRole === 'supplier' ? 'text-secondary-700' : 'text-gray-700'
                    }`}>
                      Supplier
                    </div>
                    <div className={`text-sm ${
                      watchedRole === 'supplier' ? 'text-secondary-600' : 'text-gray-500'
                    }`}>
                      Provide products and services
                    </div>
                  </div>
                  {watchedRole === 'supplier' && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute top-2 right-2"
                    >
                      <div className="w-6 h-6 bg-secondary-500 rounded-full flex items-center justify-center">
                        <Check size={14} className="text-white" />
                      </div>
                    </motion.div>
                  )}
                </motion.label>
              </div>

              {errors.role && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-2 text-sm text-red-600 text-center"
                >
                  {errors.role.message}
                </motion.p>
              )}
            </motion.div>
          </motion.div>
        );

      case 4:
        return watchedRole === 'supplier' ? (
          <motion.div
            key="step4"
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="space-y-6"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-center mb-6"
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 10, -10, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-block mb-3"
              >
                <Building size={48} className="text-secondary-500" />
              </motion.div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Business Information</h3>
              <p className="text-gray-600">Tell us about your business</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-secondary-500/10 to-third-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'storeName' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <Input
                  label="Store Name"
                  placeholder="Your business name"
                  icon={<Building size={18} className="text-secondary-500" />}
                  error={errors.storeName?.message}
                  onFocus={() => setFocusedField('storeName')}
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-secondary-300 focus:border-secondary-500 rounded-2xl transition-all duration-300"
                  {...register('storeName', {
                    required: 'Store name is required for suppliers',
                    onBlur: () => setFocusedField(null)
                  })}
                />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-third-500/10 to-primary-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'businessType' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Type
                </label>
                <select
                  className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-third-300 focus:border-third-500 rounded-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-third-500/20"
                  onFocus={() => setFocusedField('businessType')}
                  {...register('businessType', {
                    required: 'Business type is required for suppliers',
                    onBlur: () => setFocusedField(null)
                  })}
                >
                  <option value="">Select business type</option>
                  <option value="restaurant">🍽️ Restaurant</option>
                  <option value="grocery">🛒 Grocery Store</option>
                  <option value="pharmacy">💊 Pharmacy</option>
                  <option value="electronics">📱 Electronics</option>
                  <option value="clothing">👕 Clothing</option>
                  <option value="other">🏪 Other</option>
                </select>
                {errors.businessType && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-1 text-sm text-red-600"
                  >
                    {errors.businessType.message}
                  </motion.p>
                )}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="relative group"
            >
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-2xl transition-all duration-300 ${
                  focusedField === 'openHours' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                }`}
              />
              <div className="relative">
                <Input
                  label="Opening Hours"
                  placeholder="e.g., 9:00 AM - 10:00 PM"
                  icon={<Clock size={18} className="text-primary-500" />}
                  error={errors.openHours?.message}
                  onFocus={() => setFocusedField('openHours')}
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl transition-all duration-300"
                  {...register('openHours', {
                    required: 'Opening hours are required for suppliers',
                    onBlur: () => setFocusedField(null)
                  })}
                />
              </div>
            </motion.div>

            {/* Business Tips */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-gradient-to-r from-secondary-50 to-third-50 rounded-2xl p-4 border border-secondary-200"
            >
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp size={16} className="text-secondary-600" />
                <span className="text-sm font-medium text-secondary-700">Business Tips</span>
              </div>
              <div className="text-xs text-secondary-600 space-y-1">
                <p>• Choose a clear, memorable store name</p>
                <p>• Select the most accurate business category</p>
                <p>• Provide accurate opening hours for better customer experience</p>
              </div>
            </motion.div>
          </motion.div>
        ) : (
          // Location step for customers
          <motion.div
            key="step4-location"
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="space-y-8 text-center"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-6"
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="relative inline-block"
              >
                <div className="w-24 h-24 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-full flex items-center justify-center mx-auto">
                  <MapPin size={40} className="text-primary-600" />
                </div>
                <motion.div
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.3, 0.7, 0.3],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute inset-0 bg-primary-400/20 rounded-full blur-xl"
                />
              </motion.div>

              <div>
                <motion.h3
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-2xl font-bold text-gray-900 mb-3"
                >
                  Enable Location Services 📍
                </motion.h3>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className="text-gray-600 max-w-md mx-auto"
                >
                  Help us provide lightning-fast delivery services by sharing your location. This is optional but highly recommended for the best experience.
                </motion.p>
              </div>
            </motion.div>

            {userLocation ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl p-6 shadow-lg"
              >
                <div className="flex items-center justify-center space-x-3 text-green-700 mb-2">
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Check size={24} />
                  </motion.div>
                  <span className="font-bold text-lg">Location detected successfully! 🎉</span>
                </div>
                <p className="text-green-600 text-sm">
                  Coordinates: {userLocation[1].toFixed(4)}, {userLocation[0].toFixed(4)}
                </p>
                <div className="mt-3 flex items-center justify-center gap-2 text-green-600 text-sm">
                  <Zap size={16} />
                  <span>Ready for ultra-fast deliveries!</span>
                </div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="space-y-4"
              >
                <motion.div
                  className="relative"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl blur-lg opacity-40"
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [0.4, 0.6, 0.4],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <Button
                    type="button"
                    variant="primary"
                    onClick={requestLocation}
                    loading={locationLoading}
                    disabled={locationLoading}
                    className="relative z-10 w-full flex items-center justify-center space-x-3 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-bold py-4 px-8 rounded-2xl shadow-xl transition-all duration-300"
                  >
                    {locationLoading ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <MapPin size={20} />
                      </motion.div>
                    ) : (
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <MapPin size={20} />
                      </motion.div>
                    )}
                    <span className="text-lg">
                      {locationLoading ? 'Getting Location...' : 'Allow Location Access'}
                    </span>
                  </Button>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={skipLocation}
                    className="w-full bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 border border-gray-300 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 py-3"
                  >
                    Skip for Now
                  </Button>
                </motion.div>
              </motion.div>
            )}
          </motion.div>
        );

      case 5:
        return watchedRole === 'supplier' ? (
          // Location step for suppliers
          <motion.div
            key="step5-location"
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="space-y-8 text-center"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-6"
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, -5, 5, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="relative inline-block"
              >
                <div className="w-24 h-24 bg-gradient-to-br from-secondary-100 to-third-100 rounded-full flex items-center justify-center mx-auto">
                  <Building size={40} className="text-secondary-600" />
                </div>
                <motion.div
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.3, 0.7, 0.3],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute inset-0 bg-secondary-400/20 rounded-full blur-xl"
                />
                <motion.div
                  animate={{
                    rotate: [0, 360],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  className="absolute -inset-2"
                >
                  <MapPin size={16} className="absolute top-0 left-1/2 text-secondary-500" />
                </motion.div>
              </motion.div>

              <div>
                <motion.h3
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-2xl font-bold text-gray-900 mb-3"
                >
                  Set Your Store Location 🏪
                </motion.h3>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className="text-gray-600 max-w-md mx-auto"
                >
                  Help customers find your store easily by sharing your business location. This will boost your visibility and attract more customers.
                </motion.p>
              </div>
            </motion.div>

            {userLocation ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl p-6 shadow-lg"
              >
                <div className="flex items-center justify-center space-x-3 text-green-700 mb-2">
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Check size={24} />
                  </motion.div>
                  <span className="font-bold text-lg">Store location set successfully! 🎯</span>
                </div>
                <p className="text-green-600 text-sm">
                  Coordinates: {userLocation[1].toFixed(4)}, {userLocation[0].toFixed(4)}
                </p>
                <div className="mt-3 flex items-center justify-center gap-2 text-green-600 text-sm">
                  <TrendingUp size={16} />
                  <span>Ready to attract customers!</span>
                </div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="space-y-4"
              >
                <motion.div
                  className="relative"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-secondary-500 to-third-500 rounded-2xl blur-lg opacity-40"
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [0.4, 0.6, 0.4],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <Button
                    type="button"
                    variant="primary"
                    onClick={requestLocation}
                    loading={locationLoading}
                    disabled={locationLoading}
                    className="relative z-10 w-full flex items-center justify-center space-x-3 bg-gradient-to-r from-secondary-500 to-third-500 hover:from-secondary-600 hover:to-third-600 text-white font-bold py-4 px-8 rounded-2xl shadow-xl transition-all duration-300"
                  >
                    {locationLoading ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Building size={20} />
                      </motion.div>
                    ) : (
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Building size={20} />
                      </motion.div>
                    )}
                    <span className="text-lg">
                      {locationLoading ? 'Getting Location...' : 'Set Store Location'}
                    </span>
                  </Button>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={skipLocation}
                    className="w-full bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 border border-gray-300 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 py-3"
                  >
                    Skip for Now
                  </Button>
                </motion.div>
              </motion.div>
            )}
          </motion.div>
        ) : null;

      default:
        return null;
    }
  };

  return (
    <div className="relative">
      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.4, 0.1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-32 -left-32 w-64 h-64 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.3, 1, 1.3],
            opacity: [0.2, 0.5, 0.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 8
          }}
          className="absolute -bottom-32 -right-32 w-48 h-48 bg-gradient-to-br from-third-500/20 to-primary-500/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.15, 0.35, 0.15],
            rotate: [0, -180, -360],
          }}
          transition={{
            duration: 35,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 15
          }}
          className="absolute top-1/2 -left-24 w-40 h-40 bg-gradient-to-br from-secondary-500/15 to-third-500/15 rounded-full blur-3xl"
        />

        {/* Enhanced floating particles */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 bg-primary-400/40 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-15, -45, -15],
              x: [-5, 5, -5],
              opacity: [0, 1, 0],
              scale: [0.3, 1.2, 0.3],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Service icons floating */}
        {[
          { icon: UserPlus, delay: 0, x: 15, y: 25, color: 'text-primary-400' },
          { icon: Package, delay: 3, x: 85, y: 15, color: 'text-secondary-400' },
          { icon: Truck, delay: 6, x: 90, y: 75, color: 'text-third-400' },
          { icon: MapPin, delay: 9, x: 10, y: 85, color: 'text-primary-400' },
          { icon: Gift, delay: 12, x: 80, y: 45, color: 'text-secondary-400' },
          { icon: Star, delay: 15, x: 20, y: 60, color: 'text-third-400' },
        ].map((item, index) => (
          <motion.div
            key={index}
            className="absolute opacity-8"
            style={{ left: `${item.x}%`, top: `${item.y}%` }}
            animate={{
              y: [-8, 8, -8],
              x: [-3, 3, -3],
              rotate: [-8, 8, -8],
              opacity: [0.08, 0.25, 0.08],
            }}
            transition={{
              duration: 5 + Math.random() * 2,
              repeat: Infinity,
              delay: item.delay,
              ease: "easeInOut"
            }}
          >
            <item.icon size={28} className={item.color} />
          </motion.div>
        ))}
      </div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10"
      >
        {/* Enhanced Welcome Header */}
        <AnimatePresence mode="wait">
          {animationPhase === 'initial' && (
            <motion.div
              key="initial"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.1, y: -20 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-8"
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0],
                }}
                transition={{
                  duration: 2.5,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-block"
              >
                <Rocket size={56} className="text-primary-500 mx-auto mb-3" />
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-700">Preparing Your Journey...</h3>
              <p className="text-gray-500 mt-2">Setting up the ultimate signup experience</p>
            </motion.div>
          )}

          {animationPhase === 'welcome' && (
            <motion.div
              key="welcome"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-8"
            >
              <motion.div
                initial={{ scale: 0, rotate: 180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 1, type: "spring", stiffness: 120 }}
                className="inline-block mb-4"
              >
                <div className="relative">
                  <Crown size={64} className="text-primary-500 mx-auto" />
                  <motion.div
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.4, 0.8, 0.4],
                    }}
                    transition={{
                      duration: 2.5,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute inset-0 bg-primary-400/20 rounded-full blur-xl"
                  />
                  <motion.div
                    animate={{
                      rotate: [0, 360],
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                    className="absolute -inset-4"
                  >
                    <Sparkles size={20} className="absolute top-0 left-1/2 text-secondary-500" />
                    <Star size={16} className="absolute bottom-0 right-0 text-third-500" />
                    <Gem size={14} className="absolute top-1/2 left-0 text-primary-400" />
                  </motion.div>
                </div>
              </motion.div>
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-4xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent mb-2"
              >
                Join the Elite! 🚀
              </motion.h2>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="text-gray-600 font-medium"
              >
                Create your account and unlock lightning-fast deliveries
              </motion.p>
            </motion.div>
          )}

          {animationPhase === 'form' && (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              {/* Premium Welcome Header */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center mb-8"
              >
                <div className="flex items-center justify-center gap-4 mb-4">
                  <motion.div
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.2, 1],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Lightning size={36} className="text-primary-500" />
                  </motion.div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent">
                    Create Account
                  </h2>
                  <motion.div
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.6, 1, 0.6],
                    }}
                    transition={{
                      duration: 2.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1.5
                    }}
                  >
                    <Award size={32} className="text-secondary-500" />
                  </motion.div>
                </div>
                <p className="text-gray-600 font-medium mb-4">
                  Step {currentStep} of {totalSteps} - Your journey to excellence
                </p>

                {/* Enhanced Progress Bar */}
                <div className="mb-6">
                  <div className="flex justify-between mb-3">
                    {Array.from({ length: totalSteps }, (_, i) => (
                      <motion.div
                        key={i}
                        className={`relative w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-500 ${
                          i + 1 <= currentStep
                            ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg'
                            : 'bg-gray-200 text-gray-600'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        animate={i + 1 === currentStep ? {
                          scale: [1, 1.1, 1],
                          boxShadow: [
                            '0 0 0 0 rgba(117, 41, 179, 0.4)',
                            '0 0 0 10px rgba(117, 41, 179, 0)',
                            '0 0 0 0 rgba(117, 41, 179, 0)'
                          ]
                        } : {}}
                        transition={{
                          duration: 2,
                          repeat: i + 1 === currentStep ? Infinity : 0,
                        }}
                      >
                        {i + 1 < currentStep ? (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.2 }}
                          >
                            <Check size={18} />
                          </motion.div>
                        ) : (
                          i + 1
                        )}
                        {i + 1 <= currentStep && (
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-primary-400/20 to-secondary-400/20 rounded-full blur-lg"
                            animate={{
                              scale: [1, 1.5, 1],
                              opacity: [0.5, 0.8, 0.5],
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          />
                        )}
                      </motion.div>
                    ))}
                  </div>
                  <div className="relative w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                    <motion.div
                      className="bg-gradient-to-r from-primary-500 via-secondary-500 to-third-500 h-3 rounded-full relative"
                      initial={{ width: 0 }}
                      animate={{ width: `${(currentStep / totalSteps) * 100}%` }}
                      transition={{ duration: 0.8, ease: "easeOut" }}
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
                        animate={{
                          x: ['-100%', '100%'],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                    </motion.div>
                  </div>
                </div>
              </motion.div>

              {/* Enhanced Error Display */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="mb-6 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl flex items-center space-x-3 text-red-700 shadow-lg"
                >
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    <AlertCircle size={20} className="text-red-500" />
                  </motion.div>
                  <span className="text-sm font-medium">{error}</span>
                </motion.div>
              )}

              {/* Premium Form */}
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <AnimatePresence mode="wait">
                  {renderStep()}
                </AnimatePresence>

                {/* Enhanced Navigation Buttons */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="flex justify-between mt-8"
                >
                  {currentStep > 1 && (
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        type="button"
                        variant="secondary"
                        onClick={prevStep}
                        className="flex items-center space-x-2 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 border border-gray-300 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                      >
                        <ChevronLeft size={16} />
                        <span>Previous</span>
                      </Button>
                    </motion.div>
                  )}

                  <div className="ml-auto">
                    {currentStep < totalSteps ? (
                      <motion.div
                        className="relative"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl blur-lg opacity-40"
                          animate={{
                            scale: [1, 1.05, 1],
                            opacity: [0.4, 0.6, 0.4],
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                        <Button
                          type="button"
                          variant="primary"
                          onClick={nextStep}
                          className="relative z-10 flex items-center space-x-2 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-bold py-3 px-6 rounded-2xl shadow-xl transition-all duration-300"
                        >
                          <span>Next Step</span>
                          <motion.div
                            animate={{ x: [0, 3, 0] }}
                            transition={{ duration: 1.5, repeat: Infinity }}
                          >
                            <ChevronRight size={16} />
                          </motion.div>
                        </Button>
                      </motion.div>
                    ) : (
                      <motion.div
                        className="relative"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-primary-500 via-secondary-500 to-third-500 rounded-2xl blur-lg opacity-50"
                          animate={{
                            scale: [1, 1.1, 1],
                            opacity: [0.5, 0.8, 0.5],
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                        <Button
                          type="submit"
                          variant="primary"
                          loading={isLoading}
                          disabled={isLoading}
                          className="relative z-10 flex items-center space-x-3 bg-gradient-to-r from-primary-500 via-secondary-500 to-third-500 hover:from-primary-600 hover:via-secondary-600 hover:to-third-600 text-white font-bold py-4 px-8 rounded-2xl shadow-2xl transition-all duration-300"
                        >
                          {isLoading ? (
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            >
                              <Zap size={20} />
                            </motion.div>
                          ) : (
                            <motion.div
                              animate={{ scale: [1, 1.2, 1] }}
                              transition={{ duration: 2, repeat: Infinity }}
                            >
                              <Rocket size={20} />
                            </motion.div>
                          )}
                          <span className="text-lg">
                            {isLoading ? 'Creating Account...' : 'Create Account'}
                          </span>
                        </Button>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              </form>

              {/* Enhanced Sign In Link */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="mt-8 text-center"
              >
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-gray-500 font-medium">Already have an account?</span>
                  </div>
                </div>
                <motion.div
                  className="mt-4"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to="/auth/login"
                    className="group inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-semibold transition-all duration-300 bg-gradient-to-r from-primary-50 to-secondary-50 hover:from-primary-100 hover:to-secondary-100 px-6 py-3 rounded-2xl border border-primary-200 hover:border-primary-300"
                  >
                    <Shield size={16} />
                    <span>Sign In</span>
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      animate={{ x: [0, 3, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ChevronRight size={16} />
                    </motion.div>
                  </Link>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default SignupPage;
